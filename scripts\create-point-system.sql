-- Create point system tables for reward functionality
-- This script creates user_points and transaction_logs tables

-- Create user_points table to track user point balances
CREATE TABLE IF NOT EXISTS user_points (
  email TEXT PRIMARY KEY,
  points INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT points_non_negative CHECK (points >= 0)
);

-- Create transaction_logs table to track point transactions
CREATE TABLE IF NOT EXISTS transaction_logs (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL,
  transaction_type TEXT NOT NULL,
  points_change INTEGER NOT NULL,
  points_before INTEGER NOT NULL,
  points_after INTEGER NOT NULL,
  description TEXT,
  reference_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_points_email ON user_points (email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_email ON transaction_logs (email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_type ON transaction_logs (transaction_type);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_created_at ON transaction_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_reference_id ON transaction_logs (reference_id);

-- Add foreign key constraint to link transaction_logs with user_points
ALTER TABLE transaction_logs 
ADD CONSTRAINT fk_transaction_logs_email 
FOREIGN KEY (email) REFERENCES user_points(email) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE user_points IS 'Stores user point balances for the reward system';
COMMENT ON COLUMN user_points.email IS 'User email address (primary key)';
COMMENT ON COLUMN user_points.points IS 'Current point balance for the user';
COMMENT ON COLUMN user_points.created_at IS 'When the user points record was created';
COMMENT ON COLUMN user_points.updated_at IS 'When the user points record was last updated';

COMMENT ON TABLE transaction_logs IS 'Logs all point transactions for audit trail';
COMMENT ON COLUMN transaction_logs.email IS 'User email address';
COMMENT ON COLUMN transaction_logs.transaction_type IS 'Type of transaction (SIGNUP, CREATE_CONTACT, BOOKMARK_ADD, BOOKMARK_REMOVE)';
COMMENT ON COLUMN transaction_logs.points_change IS 'Points added (positive) or deducted (negative)';
COMMENT ON COLUMN transaction_logs.points_before IS 'Point balance before transaction';
COMMENT ON COLUMN transaction_logs.points_after IS 'Point balance after transaction';
COMMENT ON COLUMN transaction_logs.description IS 'Human readable description of the transaction';
COMMENT ON COLUMN transaction_logs.reference_id IS 'Reference to related entity (contact name, bookmark id, etc.)';
COMMENT ON COLUMN transaction_logs.created_at IS 'When the transaction occurred';

-- Create trigger to update updated_at timestamp for user_points
CREATE OR REPLACE FUNCTION update_user_points_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_points_updated_at
    BEFORE UPDATE ON user_points
    FOR EACH ROW
    EXECUTE FUNCTION update_user_points_updated_at();

-- Create function to safely add/subtract points with transaction logging
CREATE OR REPLACE FUNCTION update_user_points(
    user_email TEXT,
    points_change INTEGER,
    transaction_type TEXT,
    description TEXT DEFAULT NULL,
    reference_id TEXT DEFAULT NULL,
    from_email TEXT DEFAULT NULL,
    to_email TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
BEGIN
    -- Get current points or create user if doesn't exist
    INSERT INTO user_points (email, points)
    VALUES (user_email, 0)
    ON CONFLICT (email) DO NOTHING;

    SELECT points INTO current_points
    FROM user_points
    WHERE email = user_email;

    -- Calculate new points
    new_points := current_points + points_change;

    -- Check if user has enough points for deduction
    IF new_points < 0 THEN
        RETURN FALSE;
    END IF;

    -- Update user points
    UPDATE user_points
    SET points = new_points
    WHERE email = user_email;

    -- Log the transaction
    INSERT INTO transaction_logs (
        email,
        transaction_type,
        points_change,
        points_before,
        points_after,
        description,
        reference_id,
        from_email,
        to_email
    ) VALUES (
        user_email,
        transaction_type,
        points_change,
        current_points,
        new_points,
        description,
        reference_id,
        from_email,
        to_email
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Verification queries
SELECT 'Point system tables created successfully' as status;
SELECT COUNT(*) as total_user_points FROM user_points;
SELECT COUNT(*) as total_transaction_logs FROM transaction_logs;
