import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { ADMIN_EMAIL } from 'src/lib/config';
import { auth } from 'auth';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    const supabase = getSupabaseClient();

    // Get current disabled status
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('disabled')
      .eq('email', email)
      .single();

    if (fetchError) {
      console.error('Error fetching profile:', fetchError);
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Toggle the disabled status
    const newDisabledStatus = !profile.disabled;

    const { error: updateError } = await supabase
      .from('profiles')
      .update({ disabled: newDisabledStatus })
      .eq('email', email);

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
    }

    return NextResponse.json({
      message: `Profile ${newDisabledStatus ? 'disabled' : 'enabled'} successfully`,
      disabled: newDisabledStatus
    });

  } catch (error) {
    console.error('Toggle profile disable error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
