import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { auth } from 'auth';
import { parseQRData, validateServiceMetadata, generateServiceName } from 'src/lib/qr-service-utils';
import { QR_SERVICES } from 'src/lib/config';

// GET - Fetch user's QR services
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const client = getSupabaseClient();
    const { data, error } = await client
      .from('user_qr_services')
      .select('*')
      .eq('user_email', session.user.email)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching QR services:', error);
      return NextResponse.json({ error: 'Failed to fetch services' }, { status: 500 });
    }

    return NextResponse.json({ services: data || [] });
  } catch (error) {
    console.error('Error in GET /api/qr-services:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Add new QR service
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { serviceType, qrData, serviceName } = body;

    // Validate input
    if (!serviceType || !qrData) {
      return NextResponse.json({ error: 'Service type and QR data are required' }, { status: 400 });
    }

    if (!QR_SERVICES.includes(serviceType)) {
      return NextResponse.json({ error: 'Invalid service type' }, { status: 400 });
    }

    // Parse QR data
    const parsedData = parseQRData(qrData, serviceType);
    if (!parsedData) {
      return NextResponse.json({ error: 'Invalid QR code format for selected service type' }, { status: 400 });
    }

    // Validate parsed data
    const validation = validateServiceMetadata(serviceType, parsedData);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error || 'Invalid service data' }, { status: 400 });
    }

    // Generate service name if not provided
    const finalServiceName = serviceName || generateServiceName(serviceType, parsedData);

    // Check for duplicate service
    const client = getSupabaseClient();
    const { data: existingService } = await client
      .from('user_qr_services')
      .select('id')
      .eq('user_email', session.user.email)
      .eq('service_type', serviceType)
      .eq('service_name', finalServiceName)
      .eq('is_active', true)
      .single();

    if (existingService) {
      return NextResponse.json({ error: 'Service with this name already exists' }, { status: 409 });
    }

    // Insert new service
    const { data, error } = await client
      .from('user_qr_services')
      .insert({
        user_email: session.user.email,
        service_type: serviceType,
        service_name: finalServiceName,
        metadata: parsedData,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      console.error('Error inserting QR service:', error);
      return NextResponse.json({ error: 'Failed to add service' }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'Service added successfully',
      service: data
    }, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/qr-services:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT - Update existing QR service
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { serviceId, serviceName, metadata } = body;

    if (!serviceId) {
      return NextResponse.json({ error: 'Service ID is required' }, { status: 400 });
    }

    const client = getSupabaseClient();

    // Verify ownership
    const { data: existingService, error: fetchError } = await client
      .from('user_qr_services')
      .select('*')
      .eq('id', serviceId)
      .eq('user_email', session.user.email)
      .single();

    if (fetchError || !existingService) {
      return NextResponse.json({ error: 'Service not found or access denied' }, { status: 404 });
    }

    // Prepare update data
    const updateData: any = {};
    if (serviceName) updateData.service_name = serviceName;
    if (metadata) {
      // Validate metadata if provided
      const validation = validateServiceMetadata(existingService.service_type, metadata);
      if (!validation.isValid) {
        return NextResponse.json({ error: validation.error || 'Invalid service data' }, { status: 400 });
      }
      updateData.metadata = metadata;
    }

    // Update service
    const { data, error } = await client
      .from('user_qr_services')
      .update(updateData)
      .eq('id', serviceId)
      .eq('user_email', session.user.email)
      .select()
      .single();

    if (error) {
      console.error('Error updating QR service:', error);
      return NextResponse.json({ error: 'Failed to update service' }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'Service updated successfully',
      service: data
    });

  } catch (error) {
    console.error('Error in PUT /api/qr-services:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE - Remove QR service
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const serviceId = searchParams.get('id');

    if (!serviceId) {
      return NextResponse.json({ error: 'Service ID is required' }, { status: 400 });
    }

    const client = getSupabaseClient();

    // Verify ownership and delete
    const { error } = await client
      .from('user_qr_services')
      .delete()
      .eq('id', parseInt(serviceId))
      .eq('user_email', session.user.email);

    if (error) {
      console.error('Error deleting QR service:', error);
      return NextResponse.json({ error: 'Failed to delete service' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Service deleted successfully' });

  } catch (error) {
    console.error('Error in DELETE /api/qr-services:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
