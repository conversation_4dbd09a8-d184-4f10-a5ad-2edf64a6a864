"use client"
import { Page } from "src/components/Page"
import { useAuthRedirect } from "src/hooks/useAuthRedirect"
import { useEffect, useState } from "react"
import { fetchData, getSupabaseClient } from "src/lib/supabase"
import { Button, Text, Loader, Group, Paper, Title, TextInput, Textarea, Stack, Divider, ActionIcon, Tabs, Select, Card, Badge } from "@mantine/core"
import { IconArrowLeft, IconPlus, IconTrash, IconUser, IconCoin, IconBrandTwitter, IconLink, IconMail, IconWorld, IconPhone, IconRobot, IconNotes, IconBrandFacebook, IconBrandInstagram, IconBrandYoutube, IconBrandTelegram, IconBrandDiscord, IconBrandLinkedin, IconBrandTiktok, IconMapPin, IconQrcode, IconCamera, IconEdit } from "@tabler/icons-react"
import { notifications } from '@mantine/notifications'
import { useRouter, useSearchParams } from "next/navigation"
import { getSocialData, getCryptoData, prepareContactData, validateSocialHandle as utilValidateSocialHandle, validateCryptoAddress } from "src/lib/database-utils"
import { SOCIAL_SLOTS, CRYPTO_SLOTS, MAX_NOTES_CHARACTER_LIMIT, QR_SERVICES } from "src/lib/config"
import { parseQRData, validateServiceMetadata, generateServiceName, getServiceDisplayInfo } from "src/lib/qr-service-utils"
import QRCodeScanner from "src/components/QRScanner/QRCodeScanner"
import ServiceEditModal from "src/components/QRServices/ServiceEditModal"
import FullLayout from "src/components/layouts/FullLayout"

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: Record<string, string> | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  social: Record<string, string> | null;
  crypto: Record<string, string> | null;
  extra: Record<string, string> | null;
  minted: string | null;
}

export default function UpdateContactPage() {
  const { session, isLoading } = useAuthRedirect();
  const [contact, setContact] = useState<ContactItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const contactName = searchParams?.get('contact_name')?.toLowerCase();
  const [enableModify, setEnableModify] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<ContactItem>>({});
  const [links, setLinks] = useState<Record<string, { name: string; url: string }>>({});
  const [socialData, setSocialData] = useState<Record<string, string>>({});
  const [cryptoData, setCryptoData] = useState<Record<string, string>>({});
  const [notesData, setNotesData] = useState<Record<string, { title: string; content: string }>>({});
  const [locationCoordinates, setLocationCoordinates] = useState<string>('');
  const [gettingLocation, setGettingLocation] = useState(false);

  // QR Services state
  const [qrServices, setQrServices] = useState<any[]>([]);
  const [selectedServiceType, setSelectedServiceType] = useState<string>('');
  const [scannerOpened, setScannerOpened] = useState(false);
  const [loadingServices, setLoadingServices] = useState(false);
  const [editingService, setEditingService] = useState<any | null>(null);
  const [editModalOpened, setEditModalOpened] = useState(false);

  // Validation state for all fields
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Comprehensive field validation functions
  const validateField = (field: string, value: string): string | null => {
    if (!value.trim()) return null; // Empty values are allowed for most fields

    const trimmedValue = value.trim();

    // Sanitize input to prevent XSS
    const sanitizedValue = trimmedValue
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, ''); // Remove event handlers

    if (sanitizedValue !== trimmedValue) {
      return 'Invalid characters detected';
    }

    switch (field.toLowerCase()) {
      // Email validation
      case 'email':
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(trimmedValue)) {
          return 'Please enter a valid email address';
        }
        break;

      // Website URL validation
      case 'website':
        const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
        if (!urlPattern.test(trimmedValue)) {
          return 'Please enter a valid website URL';
        }
        break;

      // Phone validation (international format) - supports multiple numbers
      case 'phone':
        // Split by comma or space and validate each phone number
        const phoneNumbers = trimmedValue.split(/[,\s]+/).filter(phone => phone.trim());
        const phonePattern = /^[\+]?[1-9][\d]{0,15}$/;

        for (const phone of phoneNumbers) {
          const cleanPhone = phone.trim().replace(/[\s\-\(\)]/g, '');
          if (cleanPhone && !phonePattern.test(cleanPhone)) {
            return `Invalid phone number: "${phone.trim()}". Please enter valid phone numbers separated by commas or spaces.`;
          }
        }
        break;

      // Description length validation
      case 'description':
        if (trimmedValue.length > 200) {
          return 'Description must be less than 200 characters';
        }
        break;

      // Notes length validation
      case 'notes':
        if (trimmedValue.length > MAX_NOTES_CHARACTER_LIMIT) {
          return `Notes must be less than ${MAX_NOTES_CHARACTER_LIMIT} characters`;
        }
        break;

      // Location coordinates validation
      case 'location':
        const coordPattern = /^-?\d+\.?\d*,-?\d+\.?\d*$/;
        if (!coordPattern.test(trimmedValue)) {
          return 'Please enter valid coordinates in format: latitude,longitude (e.g., 40.7128,-74.0060)';
        }
        break;

      // Profile validation
      case 'profile':
        if (trimmedValue.length > 100) {
          return 'Profile must be less than 100 characters';
        }
        break;

      // Cryptocurrency address validation - now handled by crypto validation function
      case 'eth':
      case 'bsc':
      case 'matic':
        // Ethereum-based addresses (42 characters, starts with 0x)
        if (!/^0x[a-fA-F0-9]{40}$/.test(trimmedValue)) {
          return 'Please enter a valid Ethereum address (42 characters starting with 0x)';
        }
        break;

      case 'btc':
        // Bitcoin address validation (Legacy, SegWit, Bech32)
        const btcPattern = /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/;
        if (!btcPattern.test(trimmedValue)) {
          return 'Please enter a valid Bitcoin address';
        }
        break;

      case 'sol':
        // Solana address validation (32-44 characters, base58)
        const solPattern = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
        if (!solPattern.test(trimmedValue)) {
          return 'Please enter a valid Solana address';
        }
        break;

      case 'fil':
        // Filecoin address validation (starts with f)
        const filPattern = /^f[0-4][a-zA-Z0-9]{38,86}$/;
        if (!filPattern.test(trimmedValue)) {
          return 'Please enter a valid Filecoin address';
        }
        break;
    }

    return null; // No validation error
  };

  const validateSocialHandle = (platform: string, value: string): string | null => {
    if (!value.trim()) return null; // Empty values are allowed

    const trimmedValue = value.trim();

    // Check if it's a URL (contains protocol or obvious URL patterns)
    // Only flag obvious URLs to avoid false positives with usernames containing dots
    const urlPattern = /^(https?:\/\/|www\.)/i;
    if (urlPattern.test(trimmedValue)) {
      return `Please enter only the ${platform} handle, not a full URL`;
    }

    // Platform-specific validation
    switch (platform.toLowerCase()) {
      case 'twitter':
        const twitterHandle = trimmedValue.replace(/^@/, '');
        if (!/^[a-zA-Z0-9_]{1,15}$/.test(twitterHandle)) {
          return 'Twitter handle must be 1-15 characters, letters, numbers, and underscores only';
        }
        break;

      case 'instagram':
        const instagramHandle = trimmedValue.replace(/^@/, '');
        if (!/^[a-zA-Z0-9_.]{1,30}$/.test(instagramHandle)) {
          return 'Instagram handle must be 1-30 characters, letters, numbers, underscores, and periods only';
        }
        break;

      case 'telegram':
        const telegramHandle = trimmedValue.replace(/^@/, '');
        if (!/^[a-zA-Z0-9_]{5,32}$/.test(telegramHandle)) {
          return 'Telegram handle must be 5-32 characters, letters, numbers, and underscores only';
        }
        break;

      case 'youtube':
        const youtubeHandle = trimmedValue.replace(/^@/, '');
        // YouTube handles: 3-30 characters, letters, numbers, underscores, periods, and hyphens
        if (!/^[a-zA-Z0-9_.-]{3,30}$/.test(youtubeHandle)) {
          return 'YouTube handle must be 3-30 characters, letters, numbers, underscores, periods, and hyphens only';
        }
        break;

      case 'facebook':
        // Facebook usernames: 1-50 characters, letters, numbers, and periods only
        // Removed minimum length requirement of 5 as Facebook allows shorter usernames
        if (!/^[a-zA-Z0-9.]{1,50}$/.test(trimmedValue)) {
          return 'Facebook handle must be 1-50 characters, letters, numbers, and periods only';
        }
        break;
    }

    return null; // No validation error
  };

  const validateUrl = (value: string): string | null => {
    if (!value.trim()) return null;

    const trimmedValue = value.trim();
    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;

    if (!urlPattern.test(trimmedValue)) {
      return 'Please enter a valid URL';
    }

    return null;
  };

  const normalizeSocialHandle = (platform: string, value: string): string => {
    if (!value.trim()) return '';

    const trimmedValue = value.trim();

    // Remove @ symbol if present (we'll store without @)
    const handle = trimmedValue.replace(/^@/, '');

    return handle;
  };

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      notifications.show({
        title: 'Error',
        message: 'Geolocation is not supported by this browser',
        color: 'red',
      });
      return;
    }

    setGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const coordinates = `${latitude},${longitude}`;
        setLocationCoordinates(coordinates);

        // Clear any validation errors
        setValidationErrors(prev => ({
          ...prev,
          location: ''
        }));

        setGettingLocation(false);
        notifications.show({
          title: 'Success',
          message: 'Location coordinates obtained successfully',
          color: 'green',
        });
      },
      (error) => {
        setGettingLocation(false);
        let errorMessage = 'Failed to get location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }

        notifications.show({
          title: 'Location Error',
          message: errorMessage,
          color: 'red',
        });
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };



  useEffect(() => {
    if (!contactName) {
      setError('Invalid contact name');
      setLoading(false);
      return;
    }

    const fetchContactData = async () => {
      try {
        console.log('Fetching contact:', contactName);
        const client = getSupabaseClient();
        const { data, error } = await client
          .from('contact')
          .select('*')
          .eq('name', contactName)
          .single();

        if (error) {
          throw error;
        }

        if (data) {
          setContact(data);

          // Process social and crypto data
          const processedData = {
            ...data,
            social: data.social ? (typeof data.social === 'string' ? JSON.parse(data.social) : data.social) : {},
            crypto: data.crypto ? (typeof data.crypto === 'string' ? JSON.parse(data.crypto) : data.crypto) : {}
          };

          // Get social, crypto, and notes data using utility functions
          const social = getSocialData(processedData);
          const crypto = getCryptoData(processedData);

          setSocialData(social);
          setCryptoData(crypto);

          // Handle notes data
          if (data.notes) {
            try {
              const parsedNotes = typeof data.notes === 'string'
                ? JSON.parse(data.notes)
                : data.notes;

              // Convert old format to new format with stable IDs
              if (typeof parsedNotes === 'object' && parsedNotes !== null) {
                const convertedNotes: Record<string, { title: string; content: string }> = {};
                let index = 1;

                Object.entries(parsedNotes).forEach(([title, content]) => {
                  convertedNotes[`note${index}`] = {
                    title: title,
                    content: content as string
                  };
                  index++;
                });

                setNotesData(convertedNotes);
              } else {
                setNotesData({});
              }
            } catch (e) {
              console.error('Error parsing notes:', e);
              setNotesData({});
            }
          } else {
            // No notes data, initialize empty
            setNotesData({});
          }

          setFormData({
            description: data.description || '',
            image: data.image || '',
            uri: data.uri || '',
            profile: data.profile || '',
            email: data.email || '',
            website: data.website || '',
            phone: data.phone || '',
            tg_bot: data.tg_bot || '',
            notes: data.notes || '',
            web2: data.web2 || '',
            web3: data.web3 || ''
          });

          // Handle extra data (location coordinates)
          if (data.extra) {
            try {
              const parsedExtra = typeof data.extra === 'string'
                ? JSON.parse(data.extra)
                : data.extra;

              if (parsedExtra && parsedExtra.map) {
                setLocationCoordinates(parsedExtra.map);
              }
            } catch (e) {
              console.error('Error parsing extra data:', e);
              setLocationCoordinates('');
            }
          } else {
            setLocationCoordinates('');
          }

          // Clear any existing validation errors when loading data
          setValidationErrors({});

          const sessionEmail = session?.user?.email;
          //console.log('Session email:', sessionEmail);
          // console.log('Contact email:', data.profile_email);
          if (sessionEmail && data.profile_email === sessionEmail) {
            console.log('User is the owner of this contact');
            setEnableModify(true);
          }

          // Handle links as JSON objects
          if (data.links) {
            try {
              // Handle both string JSON and object formats
              const parsedLinks = typeof data.links === 'string'
                ? JSON.parse(data.links)
                : data.links;

              // Convert old format to new format with stable IDs
              if (typeof parsedLinks === 'object' && parsedLinks !== null) {
                const convertedLinks: Record<string, { name: string; url: string }> = {};
                let index = 1;

                Object.entries(parsedLinks).forEach(([name, url]) => {
                  convertedLinks[`link${index}`] = {
                    name: name,
                    url: url as string
                  };
                  index++;
                });

                setLinks(convertedLinks);
              } else {
                setLinks({});
              }
            } catch (e) {
              console.error('Error parsing links:', e);
              setLinks({});
            }
          } else {
            // No links data, initialize empty
            setLinks({});
          }
        } else {
          setError(`Contact not found: ${contactName}. Check if name matches exactly.`);
        }
      } catch (error) {
        console.error('Error fetching contact data:', error);
        setError(`Failed to fetch contact data`);
      } finally {
        setLoading(false);
      }
    };

    fetchContactData();
  }, [contactName]);

  // Load QR services when session is available
  useEffect(() => {
    if (session?.user?.email) {
      loadQRServices();
    }
  }, [session?.user?.email]);

  const handleInputChange = (field: string, value: string) => {
    // Check if this is a social or crypto field
    if (SOCIAL_SLOTS.includes(field)) {
      handleSocialChange(field, value);
    } else if (CRYPTO_SLOTS.includes(field)) {
      handleCryptoChange(field, value);
    } else if (field === 'location') {
      // Handle location coordinates
      const validationError = validateField(field, value);
      setValidationErrors(prev => ({
        ...prev,
        location: validationError || ''
      }));
      setLocationCoordinates(value);
    } else {
      // Regular form field
      const validationError = validateField(field, value);
      setValidationErrors(prev => ({
        ...prev,
        [field]: validationError || ''
      }));
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSocialChange = (platform: string, value: string) => {
    // Validate social handle
    const validationError = validateSocialHandle(platform, value);
    setValidationErrors(prev => ({
      ...prev,
      [platform]: validationError || ''
    }));

    // Normalize and update social data
    const normalizedValue = normalizeSocialHandle(platform, value);
    setSocialData(prev => ({
      ...prev,
      [platform]: normalizedValue
    }));
  };

  const handleCryptoChange = (currency: string, value: string) => {
    // Validate crypto address
    const validationError = validateField(currency, value);
    setValidationErrors(prev => ({
      ...prev,
      [currency]: validationError || ''
    }));

    // Update crypto data
    setCryptoData(prev => ({
      ...prev,
      [currency]: value.trim()
    }));
  };

  const handleAddLink = () => {
    // Generate a unique key for the new link
    const newKey = `link${Object.keys(links).length + 1}`;
    setLinks({ ...links, [newKey]: { name: '', url: '' } });
  };

  const handleLinkKeyChange = (linkId: string, newName: string) => {
    setLinks(prev => ({
      ...prev,
      [linkId]: { ...prev[linkId], name: newName }
    }));
  };

  const handleLinkValueChange = (linkId: string, newUrl: string) => {
    // Validate URL
    const validationError = validateUrl(newUrl);

    // Update validation errors for this link
    setValidationErrors(prev => ({
      ...prev,
      [`link_${linkId}`]: validationError || ''
    }));

    setLinks(prev => ({
      ...prev,
      [linkId]: { ...prev[linkId], url: newUrl }
    }));
  };

  const handleRemoveLink = (linkId: string) => {
    const newLinks = { ...links };
    delete newLinks[linkId];
    setLinks(newLinks);

    // Remove validation error for this link
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`link_${linkId}`];
      return newErrors;
    });
  };

  // Notes handlers
  const handleAddNote = () => {
    // Generate a unique key for the new note
    const newKey = `note${Object.keys(notesData).length + 1}`;
    setNotesData({ ...notesData, [newKey]: { title: '', content: '' } });
  };

  const handleNoteTitleChange = (noteId: string, newTitle: string) => {
    setNotesData(prev => ({
      ...prev,
      [noteId]: { ...prev[noteId], title: newTitle }
    }));
  };

  const handleNoteContentChange = (noteId: string, newContent: string) => {
    // Validate note content length
    if (newContent.length > MAX_NOTES_CHARACTER_LIMIT) {
      setValidationErrors(prev => ({
        ...prev,
        [`note_${noteId}`]: `Note content must be ${MAX_NOTES_CHARACTER_LIMIT} characters or less`
      }));
    } else {
      setValidationErrors(prev => ({
        ...prev,
        [`note_${noteId}`]: ''
      }));
    }

    setNotesData(prev => ({
      ...prev,
      [noteId]: { ...prev[noteId], content: newContent }
    }));
  };

  const handleRemoveNote = (noteId: string) => {
    const newNotes = { ...notesData };
    delete newNotes[noteId];
    setNotesData(newNotes);

    // Remove validation error for this note
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`note_${noteId}`];
      return newErrors;
    });
  };

  // QR Services handlers
  const loadQRServices = async () => {
    if (!session?.user?.email) return;

    setLoadingServices(true);
    try {
      const client = getSupabaseClient();
      const { data, error } = await client
        .from('user_qr_services')
        .select('*')
        .eq('user_email', session.user.email)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setQrServices(data || []);
    } catch (error) {
      console.error('Error loading QR services:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load QR services',
        color: 'red',
      });
    } finally {
      setLoadingServices(false);
    }
  };

  const handleAddQRService = () => {
    if (!selectedServiceType) {
      notifications.show({
        title: 'Service Required',
        message: 'Please select a service type first',
        color: 'orange',
      });
      return;
    }
    setScannerOpened(true);
  };

  const handleQRScanSuccess = async (qrData: string) => {
    if (!session?.user?.email || !selectedServiceType) return;

    try {
      // Parse QR data based on selected service type
      const parsedData = parseQRData(qrData, selectedServiceType);
      if (!parsedData) {
        throw new Error('Invalid QR code format for selected service type');
      }

      // Validate the parsed data
      const validation = validateServiceMetadata(selectedServiceType, parsedData);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid service data');
      }

      // Generate service name
      const serviceName = generateServiceName(selectedServiceType, parsedData);

      // Save to database
      const client = getSupabaseClient();
      const { error } = await client
        .from('user_qr_services')
        .insert({
          user_email: session.user.email,
          service_type: selectedServiceType,
          service_name: serviceName,
          metadata: parsedData,
          is_active: true
        });

      if (error) throw error;

      notifications.show({
        title: 'Service Added',
        message: `${serviceName} has been added successfully`,
        color: 'green',
      });

      // Reload services and reset form
      await loadQRServices();
      setSelectedServiceType('');
      setScannerOpened(false);

    } catch (error) {
      console.error('Error adding QR service:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to add service',
        color: 'red',
      });
    }
  };

  const handleEditQRService = (service: any) => {
    setEditingService(service);
    setEditModalOpened(true);
  };

  const handleRemoveQRService = async (serviceId: number) => {
    try {
      const response = await fetch(`/api/qr-services?id=${serviceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to remove service');
      }

      notifications.show({
        title: 'Service Removed',
        message: 'Service has been removed successfully',
        color: 'green',
      });

      await loadQRServices();
    } catch (error) {
      console.error('Error removing QR service:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to remove service',
        color: 'red',
      });
    }
  };

  const handleUpdate = async () => {
    if (!contactName) return;

    // Check for validation errors in all fields
    const hasValidationErrors = Object.values(validationErrors).some(error => error !== '');
    if (hasValidationErrors) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please fix validation errors before updating',
        color: 'red',
      });
      return;
    }

    setUpdating(true);
    try {
      const client = getSupabaseClient();

      // Prepare update data - convert links and notes back to name-value format for database
      const convertedLinks: Record<string, string> = {};
      Object.values(links).forEach(link => {
        if (link.name && link.url) {
          convertedLinks[link.name] = link.url;
        }
      });

      const convertedNotes: Record<string, string> = {};
      Object.values(notesData).forEach(note => {
        if (note.title && note.content) {
          convertedNotes[note.title] = note.content;
        }
      });

      // Prepare extra data (location coordinates)
      const extraData: Record<string, string> = {};
      if (locationCoordinates && locationCoordinates.trim()) {
        extraData.map = locationCoordinates.trim();
      }

      const updateData = {
        ...formData,
        links: Object.keys(convertedLinks).length > 0 ? convertedLinks : null,
        notes: Object.keys(convertedNotes).length > 0 ? convertedNotes : null,
        social: Object.keys(socialData).some(key => socialData[key]?.trim()) ? socialData : null,
        crypto: Object.keys(cryptoData).some(key => cryptoData[key]?.trim()) ? cryptoData : null,
        extra: Object.keys(extraData).length > 0 ? extraData : null
      };

      // Update the contact
      const { error } = await client
        .from('contact')
        .update(updateData)
        .eq('name', contactName);

      if (error) {
        throw error;
      }

      notifications.show({
        title: 'Success',
        message: 'Contact updated successfully',
        color: 'green',
      });

      // Navigate back after a short delay
      setTimeout(() => {
        router.push(`/tools/${contactName?.toLowerCase()}`);
      }, 2000);
    } catch (error) {
      console.error('Error updating contact:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update contact',
        color: 'red',
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  // Show loading while authentication is being checked
  if (isLoading) {
    return (
      <Page>
        <Card>
          <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
            <Loader />
          </div>
        </Card>
      </Page>
    );
  }

  // If not authenticated, the useAuthRedirect hook will handle the redirect
  if (!session) {
    return null;
  }
  if (!enableModify) {
    return (
      <FullLayout>
        <Text ta="center" size="lg" fw={500}>
          You are not authorized to modify this contact

        </Text>
      </FullLayout>
    );
  }

  return (
    <FullLayout>
      <Group mb="md">
        <Button
          variant="subtle"
          onClick={handleBack}
          leftSection={<IconArrowLeft size={16} />}
        >
          Back
        </Button>
        <Title order={2}>Update: {contactName}</Title>
      </Group>

      <Divider mb="xs" />

      {error && (
        <Text c="red" ta="center" mb="md">
          {error}
        </Text>
      )}

      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
          <Loader />
        </div>
      ) : contact ? (
        <div style={{ maxHeight: '40rem', overflowY: 'auto', padding: '0 1rem' }}>
          <Tabs defaultValue="basic" mb="xl">
            <Tabs.List>
              <Tabs.Tab value="basic" leftSection={<IconUser size={16} />}>Basic Info</Tabs.Tab>
              <Tabs.Tab value="crypto" leftSection={<IconCoin size={16} />}>Crypto</Tabs.Tab>
              <Tabs.Tab value="social" leftSection={<IconBrandTwitter size={16} />}>Social</Tabs.Tab>
              <Tabs.Tab value="links" leftSection={<IconLink size={16} />}>Links</Tabs.Tab>
              <Tabs.Tab value="qr-services" leftSection={<IconQrcode size={16} />}>QR Services</Tabs.Tab>
              <Tabs.Tab value="notes" leftSection={<IconNotes size={16} />}>Notes</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="basic" pt="md">
              <div >
                <TextInput
                  label="Profile Name"
                  value={formData.profile || ''}
                  onChange={(e) => handleInputChange('profile', e.target.value)}
                  placeholder="Enter profile (max 100 characters)"
                  leftSection={<IconUser size={16} />}
                  error={validationErrors.profile}
                />

                <TextInput
                  label="Description"
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter description (max 200 characters)"
                  disabled={!enableModify}
                  error={validationErrors.description}
                />


                <Group grow>
                  <TextInput
                    label="Email Address"
                    value={formData.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                    leftSection={<IconMail size={16} />}
                    error={validationErrors.email}
                  />


                  <TextInput
                    label="Website"
                    value={formData.website || ''}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="Enter website URL (e.g., https://example.com)"
                    leftSection={<IconWorld size={16} />}
                    error={validationErrors.website}
                  />
                </Group>

                <TextInput
                  label="Phone Numbers"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="e.g., +1234567890, +0987654321"
                  leftSection={<IconPhone size={16} />}
                  error={validationErrors.phone}
                  description="Multiple phone numbers separated by commas or spaces"
                />



                <div>
                  <Text size="sm" fw={500} mb={5}>Location Coordinates</Text>
                  <Group gap="xs">
                    <TextInput
                      value={locationCoordinates}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      placeholder="Click button to get current location or enter manually (e.g., 40.7128,-74.0060)"
                      leftSection={<IconMapPin size={16} />}
                      error={validationErrors.location}
                      style={{ flex: '0 0 70%' }}
                    />
                    <Button
                      variant="filled"
                      color="blue"
                      onClick={getCurrentLocation}
                      loading={gettingLocation}
                      leftSection={<IconMapPin size={16} />}
                      style={{ flex: '0 0 28%' }}
                    >
                      Get Location
                    </Button>
                  </Group>
                </div>

              </div>
            </Tabs.Panel>

            <Tabs.Panel value="crypto" pt="md">
              <Stack gap="md">
                {CRYPTO_SLOTS.map(currency => {
                  const getPlaceholder = (currency: string) => {
                    switch (currency.toLowerCase()) {
                      case 'eth':
                        return 'Enter Ethereum address (0x...)';
                      case 'bsc':
                        return 'Enter BSC address (0x...)';
                      case 'matic':
                        return 'Enter Polygon address (0x...)';
                      case 'btc':
                        return 'Enter Bitcoin address';
                      case 'fil':
                        return 'Enter Filecoin address (f...)';
                      case 'sol':
                        return 'Enter Solana address';
                      default:
                        return `Enter ${currency.toUpperCase()} address`;
                    }
                  };

                  return (
                    <TextInput
                      key={currency}
                      label={currency.toUpperCase()}
                      value={cryptoData[currency] || ''}
                      onChange={(e) => handleInputChange(currency, e.target.value)}
                      placeholder={getPlaceholder(currency)}
                      leftSection={<IconCoin size={16} />}
                      error={validationErrors[currency]}
                    />
                  );
                })}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="social" pt="md">
              <Stack gap="md">
                {SOCIAL_SLOTS.map(platform => {
                  const getSocialIcon = (platform: string) => {
                    switch (platform.toLowerCase()) {
                      case 'twitter':
                        return <IconBrandTwitter size={16} />;
                      case 'telegram':
                        return <IconBrandTelegram size={16} />;
                      case 'youtube':
                        return <IconBrandYoutube size={16} />;
                      case 'instagram':
                        return <IconBrandInstagram size={16} />;
                      case 'facebook':
                        return <IconBrandFacebook size={16} />;
                      case 'discord':
                        return <IconBrandDiscord size={16} />;
                      case 'linkedin':
                        return <IconBrandLinkedin size={16} />;
                      case 'tiktok':
                        return <IconBrandTiktok size={16} />;
                      default:
                        return <IconLink size={16} />;
                    }
                  };

                  const getPlaceholder = (platform: string) => {
                    switch (platform.toLowerCase()) {
                      case 'twitter':
                        return 'Enter Twitter handle (e.g., username or @username)';
                      case 'telegram':
                        return 'Enter Telegram handle (e.g., username or @username)';
                      case 'youtube':
                        return 'Enter YouTube handle (3-30 chars, e.g., @channelname)';
                      case 'instagram':
                        return 'Enter Instagram handle (e.g., username or @username)';
                      case 'facebook':
                        return 'Enter Facebook username (e.g., john.doe or john123)';
                      case 'discord':
                        return 'Enter Discord username';
                      default:
                        return `Enter ${platform} handle`;
                    }
                  };

                  return (
                    <TextInput
                      key={platform}
                      label={platform.charAt(0).toUpperCase() + platform.slice(1)}
                      value={socialData[platform] || ''}
                      onChange={(e) => handleInputChange(platform, e.target.value)}
                      placeholder={getPlaceholder(platform)}
                      leftSection={getSocialIcon(platform)}
                      error={validationErrors[platform]}
                    />
                  );
                })}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="links" pt="md">
              <Stack gap="md">
                {Object.entries(links).map(([linkId, linkData]) => (
                  <div key={linkId}>
                    <Group grow>
                      <TextInput
                        value={linkData.name}
                        onChange={(e) => handleLinkKeyChange(linkId, e.target.value)}
                        placeholder="Enter link name"
                        leftSection={<IconUser size={16} />}
                      />
                      <TextInput
                        value={linkData.url}
                        onChange={(e) => handleLinkValueChange(linkId, e.target.value)}
                        placeholder="Enter link URL (e.g., https://example.com)"
                        leftSection={<IconLink size={16} />}
                        error={validationErrors[`link_${linkId}`]}
                      />
                      <ActionIcon c="red" onClick={() => handleRemoveLink(linkId)}>
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </div>
                ))}

                <Button
                  leftSection={<IconPlus size={16} />}
                  variant="outline"
                  onClick={handleAddLink}
                >
                  Add Link
                </Button>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="notes" pt="md">
              <Stack gap="md">
                {Object.entries(notesData).map(([noteId, noteData]) => (
                  <div key={noteId}>
                    <Group grow>
                      <TextInput
                        value={noteData.title}
                        onChange={(e) => handleNoteTitleChange(noteId, e.target.value)}
                        placeholder="Enter note title"
                        leftSection={<IconNotes size={16} />}
                      />
                      <ActionIcon c="red" onClick={() => handleRemoveNote(noteId)}>
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                    <Textarea
                      value={noteData.content}
                      onChange={(e) => handleNoteContentChange(noteId, e.target.value)}
                      placeholder={`Enter note content (max ${MAX_NOTES_CHARACTER_LIMIT} characters)`}
                      error={validationErrors[`note_${noteId}`]}
                      minRows={3}
                      maxRows={6}
                      autosize
                      description={`${noteData.content.length}/${MAX_NOTES_CHARACTER_LIMIT} characters`}
                      mt="xs"
                    />
                  </div>
                ))}

                <Button
                  leftSection={<IconPlus size={16} />}
                  variant="outline"
                  onClick={handleAddNote}
                >
                  Add Note
                </Button>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="qr-services" pt="md">
              <Stack gap="md">
                <Group>
                  <Select
                    placeholder="Select service type"
                    value={selectedServiceType}
                    onChange={(value) => setSelectedServiceType(value || '')}
                    data={QR_SERVICES.map(service => ({
                      value: service,
                      label: getServiceDisplayInfo(service).name
                    }))}
                    leftSection={<IconQrcode size={16} />}
                    style={{ flex: 1 }}
                  />
                  <Button
                    leftSection={<IconCamera size={16} />}
                    onClick={handleAddQRService}
                    disabled={!selectedServiceType}
                  >
                    Scan QR
                  </Button>
                </Group>

                {loadingServices ? (
                  <div style={{ display: 'flex', justifyContent: 'center', padding: '2rem' }}>
                    <Loader />
                  </div>
                ) : (
                  <Stack gap="sm">
                    {qrServices.length === 0 ? (
                      <Text c="dimmed" ta="center" py="xl">
                        No QR services added yet. Select a service type and scan a QR code to get started.
                      </Text>
                    ) : (
                      qrServices.map((service) => {
                        const displayInfo = getServiceDisplayInfo(service.service_type);
                        return (
                          <Card key={service.id} withBorder>
                            <Group justify="space-between">
                              <Group>
                                <div style={{
                                  width: 32,
                                  height: 32,
                                  backgroundColor: `var(--mantine-color-${displayInfo.color}-1)`,
                                  borderRadius: '50%',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}>
                                  <IconQrcode size={16} color={`var(--mantine-color-${displayInfo.color}-6)`} />
                                </div>
                                <div>
                                  <Text fw={500}>{service.service_name}</Text>
                                  <Text size="sm" c="dimmed">{displayInfo.name}</Text>
                                </div>
                              </Group>
                              <Group gap="xs">
                                <Badge color={displayInfo.color} variant="light">
                                  {service.service_type.toUpperCase()}
                                </Badge>
                                <ActionIcon
                                  color="blue"
                                  variant="subtle"
                                  onClick={() => handleEditQRService(service)}
                                  title="Edit service"
                                >
                                  <IconEdit size={16} />
                                </ActionIcon>
                                <ActionIcon
                                  color="red"
                                  variant="subtle"
                                  onClick={() => handleRemoveQRService(service.id)}
                                  title="Remove service"
                                >
                                  <IconTrash size={16} />
                                </ActionIcon>
                              </Group>
                            </Group>
                          </Card>
                        );
                      })
                    )}
                  </Stack>
                )}
              </Stack>
            </Tabs.Panel>
          </Tabs>

          <Button
            onClick={handleUpdate}
            loading={updating}
            fullWidth
            mt="xl"
          >
            Update Contact
          </Button>
        </div>
      ) : null}

      {/* QR Code Scanner Modal */}
      <QRCodeScanner
        opened={scannerOpened}
        onClose={() => setScannerOpened(false)}
        onScanSuccess={handleQRScanSuccess}
        title={`Scan ${selectedServiceType ? getServiceDisplayInfo(selectedServiceType).name : 'QR'} Code`}
        expectedServiceType={selectedServiceType}
      />

      {/* Service Edit Modal */}
      <ServiceEditModal
        opened={editModalOpened}
        onClose={() => {
          setEditModalOpened(false);
          setEditingService(null);
        }}
        service={editingService}
        onServiceUpdated={loadQRServices}
      />
    </FullLayout>
  );
}