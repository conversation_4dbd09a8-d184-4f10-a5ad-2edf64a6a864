-- Add function to update user points without transaction logging (for bookmarks)
-- This function only updates the points balance without creating transaction logs

CREATE OR REPLACE FUNCTION update_user_points_only(
    user_email TEXT,
    points_change INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
BEGIN
    -- Get current points or create user if doesn't exist
    INSERT INTO user_points (email, points)
    VALUES (user_email, 0)
    ON CONFLICT (email) DO NOTHING;

    SELECT points INTO current_points
    FROM user_points
    WHERE email = user_email;

    -- Calculate new points
    new_points := current_points + points_change;

    -- Check if user has enough points for deduction
    IF new_points < 0 THEN
        RETURN FALSE;
    END IF;

    -- Update user points (no transaction logging)
    UPDATE user_points
    SET points = new_points
    WHERE email = user_email;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add comment for documentation
COMMENT ON FUNCTION update_user_points_only(TEXT, INTEGER) IS 'Updates user points without creating transaction logs - used for bookmark points';

-- Verification
SELECT 'update_user_points_only function created successfully' as status;
