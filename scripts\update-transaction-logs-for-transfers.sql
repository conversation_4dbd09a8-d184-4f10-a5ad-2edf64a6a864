-- Update transaction_logs table to support point transfers
-- This script adds from_email and to_email columns for transfer tracking

-- Add new columns for point transfers
ALTER TABLE transaction_logs 
ADD COLUMN IF NOT EXISTS from_email TEXT,
ADD COLUMN IF NOT EXISTS to_email TEXT;

-- Add indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_transaction_logs_from_email ON transaction_logs (from_email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_to_email ON transaction_logs (to_email);

-- Add comments for the new columns
COMMENT ON COLUMN transaction_logs.from_email IS 'Email of user sending points (for transfers)';
COMMENT ON COLUMN transaction_logs.to_email IS 'Email of user receiving points (for transfers)';

-- Update the update_user_points function to support transfers
CREATE OR REPLACE FUNCTION update_user_points(
    user_email TEXT,
    points_change INTEGER,
    transaction_type TEXT,
    description TEXT DEFAULT NULL,
    reference_id TEXT DEFAULT NULL,
    from_email TEXT DEFAULT NULL,
    to_email TEXT DEFAULT NULL
)
R<PERSON>URNS BOOLEAN AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
BEGIN
    -- Get current points or create user if doesn't exist
    INSERT INTO user_points (email, points) 
    VALUES (user_email, 0) 
    ON CONFLICT (email) DO NOTHING;
    
    SELECT points INTO current_points 
    FROM user_points 
    WHERE email = user_email;
    
    -- Calculate new points
    new_points := current_points + points_change;
    
    -- Check if user has enough points for deduction
    IF new_points < 0 THEN
        RETURN FALSE;
    END IF;
    
    -- Update user points
    UPDATE user_points 
    SET points = new_points 
    WHERE email = user_email;
    
    -- Log the transaction
    INSERT INTO transaction_logs (
        email, 
        transaction_type, 
        points_change, 
        points_before, 
        points_after, 
        description, 
        reference_id,
        from_email,
        to_email
    ) VALUES (
        user_email, 
        transaction_type, 
        points_change, 
        current_points, 
        new_points, 
        description, 
        reference_id,
        from_email,
        to_email
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create a function specifically for point transfers
CREATE OR REPLACE FUNCTION transfer_points(
    sender_email TEXT,
    recipient_email TEXT,
    points_amount INTEGER,
    description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    sender_current_points INTEGER;
    transfer_id TEXT;
BEGIN
    -- Generate a unique transfer ID
    transfer_id := 'TRANSFER_' || extract(epoch from now())::text || '_' || floor(random() * 1000)::text;
    
    -- Check if sender has enough points
    SELECT points INTO sender_current_points 
    FROM user_points 
    WHERE email = sender_email;
    
    IF sender_current_points IS NULL OR sender_current_points < points_amount THEN
        RETURN FALSE;
    END IF;
    
    -- Check if recipient exists in user_points (has a profile)
    IF NOT EXISTS (SELECT 1 FROM user_points WHERE email = recipient_email) THEN
        RETURN FALSE;
    END IF;
    
    -- Deduct points from sender
    IF NOT update_user_points(
        sender_email, 
        -points_amount, 
        'TRANSFER_SEND', 
        COALESCE(description, 'Points transferred to ' || recipient_email),
        transfer_id,
        sender_email,
        recipient_email
    ) THEN
        RETURN FALSE;
    END IF;
    
    -- Add points to recipient
    IF NOT update_user_points(
        recipient_email, 
        points_amount, 
        'TRANSFER_RECEIVE', 
        COALESCE(description, 'Points received from ' || sender_email),
        transfer_id,
        sender_email,
        recipient_email
    ) THEN
        -- Rollback sender transaction if recipient transaction fails
        PERFORM update_user_points(
            sender_email, 
            points_amount, 
            'TRANSFER_ROLLBACK', 
            'Transfer failed - points restored',
            transfer_id
        );
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Verification
SELECT 'Transaction logs updated for transfers successfully' as status;
