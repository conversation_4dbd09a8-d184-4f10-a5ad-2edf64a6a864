'use client';
import { useState, useEffect } from 'react';
import { Modal, TextInput, Button, Stack, Group, Text, Alert } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconCheck, IconX } from '@tabler/icons-react';
import { getServiceDisplayInfo } from 'src/lib/qr-service-utils';

interface ServiceEditModalProps {
  opened: boolean;
  onClose: () => void;
  service: any | null;
  onServiceUpdated: () => void;
}

export default function ServiceEditModal({ opened, onClose, service, onServiceUpdated }: ServiceEditModalProps) {
  const [serviceName, setServiceName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset form when modal opens/closes or service changes
  useEffect(() => {
    if (opened && service) {
      setServiceName(service.service_name || '');
      setError(null);
    } else {
      setServiceName('');
      setError(null);
    }
  }, [opened, service]);

  const handleSave = async () => {
    if (!service || !serviceName.trim()) {
      setError('Service name is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/qr-services', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceId: service.id,
          serviceName: serviceName.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update service');
      }

      notifications.show({
        title: 'Service Updated',
        message: 'Service has been updated successfully',
        color: 'green',
        icon: <IconCheck size={16} />,
      });

      onServiceUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating service:', error);
      setError(error instanceof Error ? error.message : 'Failed to update service');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  if (!service) return null;

  const displayInfo = getServiceDisplayInfo(service.service_type);

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={`Edit ${displayInfo.name} Service`}
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="sm" c="dimmed">
          Update the display name for your {displayInfo.name.toLowerCase()} service.
        </Text>

        {error && (
          <Alert icon={<IconAlertCircle size={16} />} color="red">
            {error}
          </Alert>
        )}

        <TextInput
          label="Service Name"
          placeholder="Enter a name for this service"
          value={serviceName}
          onChange={(e) => setServiceName(e.target.value)}
          error={!serviceName.trim() ? 'Service name is required' : ''}
          disabled={loading}
        />

        <Text size="xs" c="dimmed">
          <strong>Service Type:</strong> {displayInfo.name}
        </Text>

        <Group justify="flex-end">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
            leftSection={<IconX size={16} />}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            loading={loading}
            disabled={!serviceName.trim()}
            leftSection={<IconCheck size={16} />}
          >
            Save Changes
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
