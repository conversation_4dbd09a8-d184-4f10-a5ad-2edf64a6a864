-- Migration script to add individual payment service support to existing user_qr_services table
-- Run this on existing databases to add Fonepay, eSewa, Khalti, Alipay, WeChat Pay support

-- Update the CHECK constraint to include individual payment services
ALTER TABLE user_qr_services
DROP CONSTRAINT IF EXISTS user_qr_services_service_type_check;

ALTER TABLE user_qr_services
ADD CONSTRAINT user_qr_services_service_type_check
CHECK (service_type IN ('wifi', 'upi', 'fonepay', 'esewa', 'khalti', 'alipay', 'wechatpay'));

-- Add comment for documentation
COMMENT ON TABLE user_qr_services IS 'Stores QR service metadata for users. Supports WiFi, UPI, and various payment systems (Fonepay, eSewa, Khalti, Alipay, WeChat Pay)';
