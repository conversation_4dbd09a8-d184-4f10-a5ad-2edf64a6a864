import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { ADMIN_EMAIL } from 'src/lib/config';
import { auth } from 'auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;
    const search = searchParams.get('search') || '';
    const profileFilter = searchParams.get('profile') || '';
    const imageFilter = searchParams.get('image') || '';
    const mintedFilter = searchParams.get('minted') || '';

    const supabase = getSupabaseClient();

    // Build query
    let query = supabase
      .from('contact')
      .select(`
        timestamp,
        name,
        profile,
        image,
        minted,
        profile_email,
        disabled
      `)
      .order('timestamp', { ascending: false });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,profile.ilike.%${search}%`);
    }

    if (profileFilter) {
      query = query.eq('profile_email', profileFilter);
    }

    if (imageFilter) {
      if (imageFilter === 'yes') {
        query = query.not('image', 'is', null);
      } else if (imageFilter === 'no') {
        query = query.is('image', null);
      }
    }

    if (mintedFilter) {
      if (mintedFilter === 'yes') {
        query = query.not('minted', 'is', null);
      } else if (mintedFilter === 'no') {
        query = query.is('minted', null);
      }
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: contacts, error: contactsError } = await query;

    if (contactsError) {
      console.error('Error fetching contacts:', contactsError);
      return NextResponse.json({ error: 'Failed to fetch contacts' }, { status: 500 });
    }

    // Get total count for pagination (with same filters)
    let countQuery = supabase
      .from('contact')
      .select('*', { count: 'exact', head: true });

    if (search) {
      countQuery = countQuery.or(`name.ilike.%${search}%,profile.ilike.%${search}%`);
    }

    if (profileFilter) {
      countQuery = countQuery.eq('profile_email', profileFilter);
    }

    if (imageFilter) {
      if (imageFilter === 'yes') {
        countQuery = countQuery.not('image', 'is', null);
      } else if (imageFilter === 'no') {
        countQuery = countQuery.is('image', null);
      }
    }

    if (mintedFilter) {
      if (mintedFilter === 'yes') {
        countQuery = countQuery.not('minted', 'is', null);
      } else if (mintedFilter === 'no') {
        countQuery = countQuery.is('minted', null);
      }
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      console.error('Error fetching contacts count:', countError);
      return NextResponse.json({ error: 'Failed to fetch contacts count' }, { status: 500 });
    }

    return NextResponse.json({
      data: contacts || [],
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Admin contacts API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { contactName, action } = await request.json();

    if (!contactName) {
      return NextResponse.json({ error: 'Contact name is required' }, { status: 400 });
    }

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    const supabase = getSupabaseClient();

    // Handle different actions
    if (action === 'clearImage') {
      // Clear the image field (set to null)
      const { error: updateError } = await supabase
        .from('contact')
        .update({ image: null })
        .eq('name', contactName.toLowerCase());

      if (updateError) {
        console.error('Error clearing image:', updateError);
        return NextResponse.json({ error: 'Failed to clear image status' }, { status: 500 });
      }

      return NextResponse.json({ success: true, message: 'Image status cleared successfully' });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Admin patch contact API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { contactName } = await request.json();

    if (!contactName) {
      return NextResponse.json({ error: 'Contact name is required' }, { status: 400 });
    }

    const supabase = getSupabaseClient();

    // Get contact details first for cleanup
    const { data: contact, error: fetchError } = await supabase
      .from('contact')
      .select('*')
      .eq('name', contactName.toLowerCase())
      .single();

    if (fetchError) {
      console.error('Error fetching contact:', fetchError);
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Delete associated bookmarks
    const { error: bookmarkError } = await supabase
      .from('bookmark')
      .delete()
      .eq('contact_name', contactName.toLowerCase());

    if (bookmarkError) {
      console.error('Error deleting bookmarks:', bookmarkError);
    }

    // Delete the contact
    const { error: deleteError } = await supabase
      .from('contact')
      .delete()
      .eq('name', contactName.toLowerCase());

    if (deleteError) {
      console.error('Error deleting contact:', deleteError);
      return NextResponse.json({ error: 'Failed to delete contact' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Contact deleted successfully' });

  } catch (error) {
    console.error('Admin delete contact API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
