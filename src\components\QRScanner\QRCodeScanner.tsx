'use client';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Text, Stack, Alert, Group, Loader, FileInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconX, IconAlertCircle, IconUpload } from '@tabler/icons-react';
import { uploadFile, deleteFile, getFileUrl } from 'src/lib/supabase';

interface QRCodeScannerProps {
  opened: boolean;
  onClose: () => void;
  onScanSuccess: (data: string) => void;
  title?: string;
  expectedServiceType?: string; // For validation
}

export default function QRCodeScanner({
  opened,
  onClose,
  onScanSuccess,
  title = "Upload QR Code",
  expectedServiceType
}: QRCodeScannerProps) {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Validate QR data against expected service type
  const validateQRData = (qrData: string): boolean => {
    if (!expectedServiceType) return true; // No validation needed

    try {
      // Import the validation function dynamically to avoid circular imports
      const { parseQRData } = require('src/lib/qr-service-utils');
      const parsedData = parseQRData(qrData, expectedServiceType);
      return parsedData !== null;
    } catch (error) {
      console.error('Error validating QR data:', error);
      // For payment codes, be more lenient as they can have various formats
      if (['fonepay', 'esewa', 'khalti', 'alipay', 'wechatpay'].includes(expectedServiceType)) {
        return qrData.length >= 10; // Basic length check for payment codes
      }
      return false;
    }
  };

  // Handle QR code detection
  const handleQRDetected = (qrData: string) => {
    // Validate QR data if service type is specified
    if (!validateQRData(qrData)) {
      notifications.show({
        title: 'Invalid QR Code',
        message: `This QR code is not compatible with ${expectedServiceType || 'the selected service'}.`,
        color: 'red',
      });
      return;
    }

    onScanSuccess(qrData);

    notifications.show({
      title: 'QR Code Processed',
      message: 'QR code processed successfully!',
      color: 'green',
    });
  };

  // Use jsQR library instead of qr-scanner to avoid CSP issues
  const scanQRFromCanvas = (canvas: HTMLCanvasElement): string | null => {
    try {
      const jsQR = require('jsqr');
      const ctx = canvas.getContext('2d');
      if (!ctx) return null;

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const code = jsQR(imageData.data, imageData.width, imageData.height);

      return code ? code.data : null;
    } catch (error) {
      console.error('jsQR scanning error:', error);
      return null;
    }
  };

  // Handle file upload for QR scanning
  const handleFileUpload = async (file: File | null) => {
    if (!file) return;

    let uploadedFilePath: string | null = null;

    try {
      setIsLoading(true);
      setError(null);

      // Validate file type
      if (!file.type.startsWith('image/')) {
        throw new Error('Please upload a valid image file.');
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('Image file is too large. Please upload an image smaller than 10MB.');
      }

      // Upload image temporarily for processing
      const timestamp = Date.now();
      const fileName = `qr_temp_${timestamp}.${file.name.split('.').pop()}`;
      const filePath = `qr-temp/${fileName}`;
      uploadedFilePath = filePath;

      const { data: uploadData, error: uploadError } = await uploadFile(
        'images', // bucket name
        filePath,
        file
      );

      if (uploadError) {
        throw new Error('Failed to upload image for processing.');
      }

      // Get the uploaded image URL
      const imageUrl = getFileUrl('images', filePath);

      // Use canvas-based scanning with jsQR to avoid CSP issues
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('Canvas context not available');
      }

      const img = new Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = () => reject(new Error('Failed to load image'));
        img.crossOrigin = 'anonymous'; // Enable CORS for uploaded image
        img.src = imageUrl;
      });

      // Set canvas size to image size
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw image to canvas
      ctx.drawImage(img, 0, 0);

      // Scan QR code using jsQR
      const result = scanQRFromCanvas(canvas);

      if (result && typeof result === 'string') {
        handleQRDetected(result);
      } else {
        throw new Error('No QR code found in the uploaded image. Please ensure the image contains a clear, visible QR code.');
      }
    } catch (err) {
      console.error('Error scanning uploaded image:', err);
      const errorMessage = err instanceof Error ? err.message : 'No QR code found in the uploaded image.';
      setError(errorMessage);

      notifications.show({
        title: 'Upload Failed',
        message: errorMessage,
        color: 'red',
      });
    } finally {
      // Always clean up the temporary uploaded file
      if (uploadedFilePath) {
        try {
          await deleteFile('images', uploadedFilePath);
          console.log('Temporary QR image cleaned up:', uploadedFilePath);
        } catch (cleanupError) {
          console.error('Error cleaning up temporary QR image:', cleanupError);
        }
      }
      setIsLoading(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={title}
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="sm" c="dimmed">
          Upload an image containing a QR code to scan it.
        </Text>

        {error && (
          <Alert icon={<IconAlertCircle size={16} />} color="red">
            {error}
          </Alert>
        )}

        {/* File Upload Option */}
        <FileInput
          placeholder="Upload QR code image"
          accept="image/*"
          onChange={handleFileUpload}
          leftSection={<IconUpload size={16} />}
          disabled={isLoading}
        />

        {isLoading && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px'
          }}>
            <Loader size="lg" />
            <Text ml="md">Processing QR code...</Text>
          </div>
        )}

        <Group justify="space-between">
          <Button
            variant="outline"
            onClick={handleClose}
            leftSection={<IconX size={16} />}
            disabled={isLoading}
          >
            Cancel
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
