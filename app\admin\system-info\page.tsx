'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import {
  Container,
  Title,
  Text,
  Alert,
  LoadingOverlay,
  Card,
  Group,
  Badge,
  Stack,
  Table,
  Button,
  Divider,
  ThemeIcon,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconCheck,
  IconX,
  IconRefresh,
  IconDatabase,
  IconServer,
  IconCode,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { AdminLayout } from 'src/components/layouts/AdminLayout';

interface SystemCheck {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

interface SystemInfo {
  nodeVersion: string;
  nextVersion: string;
  databaseTables: string[];
  databaseFunctions: string[];
  missingTables: string[];
  missingFunctions: string[];
  configStatus: SystemCheck[];
  systemChecks: SystemCheck[];
}

export default function SystemInfoPage() {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSystemInfo = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/system-info');
      
      if (!response.ok) {
        throw new Error('Failed to fetch system information');
      }

      const data = await response.json();
      setSystemInfo(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch system information');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemInfo();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <IconCheck size={16} color="green" />;
      case 'fail':
        return <IconX size={16} color="red" />;
      case 'warning':
        return <IconAlertCircle size={16} color="orange" />;
      default:
        return <IconAlertCircle size={16} color="gray" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'green';
      case 'fail':
        return 'red';
      case 'warning':
        return 'orange';
      default:
        return 'gray';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <LoadingOverlay visible />
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconServer size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>System Information</Title>
              <Text c="dimmed">System status and configuration details</Text>
            </div>
          </Group>
          <Button
            variant="light"
            leftSection={<IconRefresh size={16} />}
            onClick={fetchSystemInfo}
            loading={loading}
          >
            Refresh
          </Button>
        </Group>

        {systemInfo && (
          <>
            {/* System Environment */}
            <Card withBorder>
              <Group mb="md">
                <IconServer size={20} />
                <Title order={3}>System Environment</Title>
              </Group>
              
              <Table>
                <Table.Tbody>
                  <Table.Tr>
                    <Table.Td><Text fw={500}>Node.js Version</Text></Table.Td>
                    <Table.Td><Badge color="blue">{systemInfo.nodeVersion}</Badge></Table.Td>
                  </Table.Tr>
                  <Table.Tr>
                    <Table.Td><Text fw={500}>Next.js Version</Text></Table.Td>
                    <Table.Td><Badge color="blue">{systemInfo.nextVersion}</Badge></Table.Td>
                  </Table.Tr>
                </Table.Tbody>
              </Table>
            </Card>

            {/* Database Status */}
            <Card withBorder>
              <Group mb="md">
                <IconDatabase size={20} />
                <Title order={3}>Database Status</Title>
              </Group>
              
              <Stack gap="md">
                <div>
                  <Text fw={500} mb="xs">Required Tables</Text>
                  <Group gap="xs">
                    {['contact', 'bookmark', 'profiles', 'settings', 'user_points', 'transaction_logs'].map(table => (
                      <Badge
                        key={table}
                        color={systemInfo.databaseTables.includes(table) ? 'green' : 'red'}
                        leftSection={systemInfo.databaseTables.includes(table) ? <IconCheck size={12} /> : <IconX size={12} />}
                      >
                        {table}
                      </Badge>
                    ))}
                  </Group>
                </div>

                <div>
                  <Text fw={500} mb="xs">Required Functions</Text>
                  <Group gap="xs">
                    {['update_user_points', 'transfer_points'].map(func => (
                      <Badge
                        key={func}
                        color={systemInfo.databaseFunctions.includes(func) ? 'green' : 'red'}
                        leftSection={systemInfo.databaseFunctions.includes(func) ? <IconCheck size={12} /> : <IconX size={12} />}
                      >
                        {func}
                      </Badge>
                    ))}
                  </Group>
                </div>

                {systemInfo.missingTables.length > 0 && (
                  <Alert color="red" title="Missing Tables">
                    The following required tables are missing: {systemInfo.missingTables.join(', ')}
                  </Alert>
                )}

                {systemInfo.missingFunctions.length > 0 && (
                  <Alert color="red" title="Missing Functions">
                    The following required functions are missing: {systemInfo.missingFunctions.join(', ')}
                  </Alert>
                )}
              </Stack>
            </Card>

            {/* Configuration Status */}
            <Card withBorder>
              <Group mb="md">
                <IconCode size={20} />
                <Title order={3}>Configuration Status</Title>
              </Group>
              
              <Table>
                <Table.Tbody>
                  {systemInfo.configStatus.map((check, index) => (
                    <Table.Tr key={index}>
                      <Table.Td>
                        <Group gap="xs">
                          {getStatusIcon(check.status)}
                          <Text>{check.name}</Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(check.status)}>
                          {check.status.toUpperCase()}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm" c="dimmed">{check.message}</Text>
                        {check.details && (
                          <Text size="xs" c="dimmed" mt={2}>{check.details}</Text>
                        )}
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Card>

            {/* System Checks */}
            <Card withBorder>
              <Group mb="md">
                <IconAlertCircle size={20} />
                <Title order={3}>System Health Checks</Title>
              </Group>
              
              <Table>
                <Table.Tbody>
                  {systemInfo.systemChecks.map((check, index) => (
                    <Table.Tr key={index}>
                      <Table.Td>
                        <Group gap="xs">
                          {getStatusIcon(check.status)}
                          <Text>{check.name}</Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(check.status)}>
                          {check.status.toUpperCase()}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm" c="dimmed">{check.message}</Text>
                        {check.details && (
                          <Text size="xs" c="dimmed" mt={2}>{check.details}</Text>
                        )}
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Card>
          </>
        )}
      </Stack>
    </AdminLayout>
  );
}
