import { NextRequest } from 'next/server';
import { auth } from 'auth';
import { ADMIN_EMAIL } from './config';

/**
 * Server-side admin authentication check
 * This function checks both the user session and admin email
 * Note: Server-side cannot check localStorage admin session, 
 * so we rely on the client-side protection for the passkey verification
 */
export async function verifyAdminAuth(request?: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return {
        isAuthorized: false,
        error: 'Unauthorized',
        status: 403
      };
    }

    return {
      isAuthorized: true,
      session,
      error: null,
      status: 200
    };
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return {
      isAuthorized: false,
      error: 'Internal server error',
      status: 500
    };
  }
}

/**
 * Middleware function to protect admin API routes
 * Usage: const authResult = await requireAdminAuth();
 * if (!authResult.isAuthorized) return NextResponse.json({ error: authResult.error }, { status: authResult.status });
 */
export async function requireAdminAuth(request?: NextRequest) {
  return await verifyAdminAuth(request);
}
