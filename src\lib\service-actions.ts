// Service Action Handlers for different QR service types

import { notifications } from '@mantine/notifications';
import { WiFiQRData, UPIQRData } from './qr-service-utils';

/**
 * Handle WiFi service action - attempt to connect to WiFi network
 */
export async function handleWiFiAction(metadata: { wifi: WiFiQRData }) {
  try {
    const { wifi } = metadata;
    
    // Check if we're in a browser environment that supports WiFi connection
    if (typeof navigator !== 'undefined' && 'credentials' in navigator) {
      // For web browsers, we can only show the WiFi details
      // Actual WiFi connection requires native mobile app capabilities
      
      const wifiDetails = [
        `Network: ${wifi.ssid}`,
        wifi.password ? `Password: ${wifi.password}` : 'No password required',
        wifi.security ? `Security: ${wifi.security}` : '',
        wifi.hidden ? 'Hidden network' : ''
      ].filter(Boolean).join('\n');

      // Copy WiFi details to clipboard
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(wifiDetails);
        notifications.show({
          title: 'WiFi Details Copied',
          message: 'WiFi network details have been copied to clipboard',
          color: 'blue',
        });
      } else {
        // Fallback for browsers without clipboard API
        notifications.show({
          title: 'WiFi Network Details',
          message: wifiDetails,
          color: 'blue',
          autoClose: false,
        });
      }
    } else {
      notifications.show({
        title: 'WiFi Connection',
        message: 'Please connect to the WiFi network manually using your device settings',
        color: 'orange',
      });
    }
  } catch (error) {
    console.error('Error handling WiFi action:', error);
    notifications.show({
      title: 'WiFi Error',
      message: 'Failed to process WiFi connection',
      color: 'red',
    });
  }
}

/**
 * Handle UPI service action - open UPI payment app or copy UPI ID
 */
export async function handleUPIAction(metadata: { upi: UPIQRData }) {
  try {
    const { upi } = metadata;
    
    // Construct UPI URL
    const upiUrl = `upi://pay?pa=${upi.pa}${upi.pn ? `&pn=${encodeURIComponent(upi.pn)}` : ''}${upi.am ? `&am=${upi.am}` : ''}${upi.cu ? `&cu=${upi.cu}` : ''}${upi.tn ? `&tn=${encodeURIComponent(upi.tn)}` : ''}`;
    
    // Try to open UPI app (works on mobile devices with UPI apps installed)
    if (typeof window !== 'undefined') {
      // Create a temporary link to trigger UPI app
      const link = document.createElement('a');
      link.href = upiUrl;
      link.style.display = 'none';
      document.body.appendChild(link);
      
      // Try to open UPI app
      try {
        link.click();
        
        // Show success message
        notifications.show({
          title: 'Opening UPI App',
          message: `Redirecting to UPI app for payment to ${upi.pn || upi.pa}`,
          color: 'green',
        });
      } catch (error) {
        // If UPI app opening fails, copy UPI ID to clipboard
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(upi.pa);
          notifications.show({
            title: 'UPI ID Copied',
            message: `UPI ID ${upi.pa} has been copied to clipboard`,
            color: 'blue',
          });
        } else {
          notifications.show({
            title: 'UPI Payment',
            message: `UPI ID: ${upi.pa}${upi.pn ? `\nPayee: ${upi.pn}` : ''}${upi.am ? `\nAmount: ₹${upi.am}` : ''}`,
            color: 'blue',
            autoClose: false,
          });
        }
      } finally {
        document.body.removeChild(link);
      }
    }
  } catch (error) {
    console.error('Error handling UPI action:', error);
    notifications.show({
      title: 'UPI Error',
      message: 'Failed to process UPI payment',
      color: 'red',
    });
  }
}

/**
 * Main service action handler - routes to appropriate service handler
 */
export async function handleServiceAction(serviceType: string, metadata: any) {
  switch (serviceType) {
    case 'wifi':
      await handleWiFiAction(metadata);
      break;
    
    case 'upi':
      await handleUPIAction(metadata);
      break;
    
    default:
      notifications.show({
        title: 'Unsupported Service',
        message: `Service type "${serviceType}" is not supported yet`,
        color: 'orange',
      });
      break;
  }
}

/**
 * Get action button text for service type
 */
export function getServiceActionText(serviceType: string): string {
  switch (serviceType) {
    case 'wifi':
      return 'Connect to WiFi';
    
    case 'upi':
      return 'Pay with UPI';
    
    default:
      return 'Use Service';
  }
}

/**
 * Get display information for service type
 */
export function getServiceDisplayInfo(serviceType: string): { name: string; color: string } {
  switch (serviceType) {
    case 'wifi':
      return { name: 'WiFi', color: 'blue' };
    case 'upi':
      return { name: 'UPI', color: 'green' };
    default:
      return { name: 'Unknown', color: 'gray' };
  }
}

/**
 * Check if service action is available in current environment
 */
export function isServiceActionAvailable(serviceType: string): boolean {
  switch (serviceType) {
    case 'wifi':
      // WiFi connection details can always be shown/copied
      return true;
    
    case 'upi':
      // UPI actions work in browser environments
      return typeof window !== 'undefined';
    
    default:
      return false;
  }
}
