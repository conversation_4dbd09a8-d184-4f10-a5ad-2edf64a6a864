export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // Define your tables here
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          full_name: string | null
          avatar_url: string | null
          email: string | null
          disabled: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
          disabled?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
          disabled?: boolean
        }
      }
      // New people table for ODude Name data with individual columns
      contact: {
        Row: {
          timestamp: string
          name: string
          image: string | null
          description: string | null
          uri: string | null
          profile: string | null
          email: string | null
          website: string | null
          phone: string | null
          tg_bot: string | null
          notes: Json | null

          web2: string | null
          web3: string | null
          links: Json | null
          images: Json | null
          social: Json | null
          crypto: Json | null
          extra: Json | null
          profile_email: string | null
          minted: string | null
          disabled: boolean
        }
        Insert: {
          timestamp?: string
          name: string
          image?: string | null
          description?: string | null
          uri?: string | null
          profile?: string | null
          email?: string | null
          website?: string | null
          phone?: string | null
          tg_bot?: string | null
          notes?: Json | null

          web2?: string | null
          web3?: string | null
          links?: Json | null
          images?: Json | null
          social?: Json | null
          crypto?: Json | null
          extra?: Json | null
          profile_email?: string | null
          minted?: string | null
          disabled?: boolean
        }
        Update: {
          timestamp?: string
          name?: string
          image?: string | null
          description?: string | null
          uri?: string | null
          profile?: string | null
          email?: string | null
          website?: string | null
          phone?: string | null
          tg_bot?: string | null
          notes?: Json | null

          web2?: string | null
          web3?: string | null
          links?: Json | null
          images?: Json | null
          social?: Json | null
          crypto?: Json | null
          extra?: Json | null
          profile_email?: string | null
          minted?: string | null
          disabled?: boolean
        }
      }
      bookmark: {
        Row: {
          id: number;
          created_at: string;
          contact_name: string;
          contact_email: string;
        };
        Insert: {
          id?: number;
          created_at?: string;
          contact_name: string;
          contact_email: string;
        };
        Update: {
          id?: number;
          created_at?: string;
          contact_name?: string;
          contact_email?: string;
        };
      }
      user_qr_services: {
        Row: {
          id: number;
          created_at: string;
          updated_at: string;
          user_email: string;
          service_type: string;
          service_name: string;
          metadata: Json;
          is_active: boolean;
        };
        Insert: {
          id?: number;
          created_at?: string;
          updated_at?: string;
          user_email: string;
          service_type: string;
          service_name: string;
          metadata: Json;
          is_active?: boolean;
        };
        Update: {
          id?: number;
          created_at?: string;
          updated_at?: string;
          user_email?: string;
          service_type?: string;
          service_name?: string;
          metadata?: Json;
          is_active?: boolean;
        };
      }
      settings: {
        Row: {
          email: string;
          max_contact_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          email: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
      }
      user_points: {
        Row: {
          email: string;
          points: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          email: string;
          points?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          points?: number;
          created_at?: string;
          updated_at?: string;
        };
      }
      transaction_logs: {
        Row: {
          id: number;
          email: string;
          transaction_type: string;
          points_change: number;
          points_before: number;
          points_after: number;
          description: string | null;
          reference_id: string | null;
          from_email: string | null;
          to_email: string | null;
          created_at: string;
        };
        Insert: {
          id?: number;
          email: string;
          transaction_type: string;
          points_change: number;
          points_before: number;
          points_after: number;
          description?: string | null;
          reference_id?: string | null;
          from_email?: string | null;
          to_email?: string | null;
          created_at?: string;
        };
        Update: {
          id?: number;
          email?: string;
          transaction_type?: string;
          points_change?: number;
          points_before?: number;
          points_after?: number;
          description?: string | null;
          reference_id?: string | null;
          from_email?: string | null;
          to_email?: string | null;
          created_at?: string;
        };
      }
      // Add more tables as needed
    }
    Views: {
      // Define your views here
    }
    Functions: {
      // Define your functions here
    }
    Enums: {
      // Define your enums here
    }
  }
}
