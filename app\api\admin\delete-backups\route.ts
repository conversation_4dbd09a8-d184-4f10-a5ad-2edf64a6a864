import { NextResponse } from 'next/server';
import { Client as FTPClient } from 'basic-ftp';
import { FTP_HOST, FTP_USER, FTP_PASSWORD, FTP_PATH, ADMIN_EMAIL } from 'src/lib/config';
import { auth } from 'auth';

/**
 * Delete all backup files from FTP server
 */
async function deleteAllBackupFiles(): Promise<{ success: boolean; message: string; deletedCount: number }> {
  const client = new FTPClient();

  try {
    // Connect to FTP server
    await client.access({
      host: FTP_HOST,
      user: FTP_USER,
      password: FTP_PASSWORD,
      secure: false
    });

    // Ensure backup directory exists
    try {
      await client.ensureDir(FTP_PATH);
    } catch (error) {
      return { success: false, message: 'Backup directory not found', deletedCount: 0 };
    }

    // List files in backup directory
    const fileList = await client.list(FTP_PATH);

    if (fileList.length === 0) {
      return { success: true, message: 'No backup files found to delete', deletedCount: 0 };
    }

    // Find backup files - look for CSV files that contain table names or backup patterns
    const backupFiles = fileList.filter(file =>
      file.name.endsWith('.csv') && (
        file.name.includes('backup_') ||
        file.name.includes('contact_') ||
        file.name.includes('bookmark_') ||
        file.name.includes('profiles_') ||
        file.name.includes('settings_') ||
        file.name.includes('user_points_') ||
        file.name.includes('transaction_logs_')
      )
    );

    if (backupFiles.length === 0) {
      return { success: true, message: 'No backup files found to delete', deletedCount: 0 };
    }

    let deletedCount = 0;
    const errors: string[] = [];

    // Delete each backup file
    for (const file of backupFiles) {
      try {
        await client.remove(`${FTP_PATH}/${file.name}`);
        deletedCount++;
        console.log(`Deleted backup file: ${file.name}`);
      } catch (error) {
        const errorMessage = `Failed to delete ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    if (errors.length > 0) {
      return {
        success: deletedCount > 0,
        message: `Deleted ${deletedCount} files, but ${errors.length} files failed to delete: ${errors.join(', ')}`,
        deletedCount
      };
    }

    return {
      success: true,
      message: `Successfully deleted ${deletedCount} backup files`,
      deletedCount
    };

  } catch (error) {
    console.error('Error deleting backup files:', error);
    return {
      success: false,
      message: `Error deleting backup files: ${error instanceof Error ? error.message : 'Unknown error'}`,
      deletedCount: 0
    };
  } finally {
    client.close();
  }
}

export async function DELETE() {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const result = await deleteAllBackupFiles();

    return NextResponse.json({
      success: result.success,
      message: result.message,
      deletedCount: result.deletedCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Delete backups API error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error',
      message: 'Failed to delete backup files'
    }, { status: 500 });
  }
}
