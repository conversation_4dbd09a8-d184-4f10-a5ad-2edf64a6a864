-- Create user_qr_services table for storing QR service metadata linked to users
-- This table stores different QR services (WiFi, UPI, etc.) that users have scanned and linked

CREATE TABLE IF NOT EXISTS user_qr_services (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  user_email TEXT NOT NULL REFERENCES profiles(email) ON DELETE CASCADE,
  service_type TEXT NOT NULL CHECK (service_type IN ('wifi', 'upi', 'fonepay', 'esewa', 'khalti', 'alipay', 'wechatpay')),
  service_name TEXT NOT NULL, -- User-friendly name for the service
  metadata JSONB NOT NULL, -- Service-specific data (SSID/password for WiFi, UPI ID for UPI, etc.)
  is_active BOOLEAN DEFAULT true,
  UNIQUE(user_email, service_type, service_name)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_qr_services_user_email ON user_qr_services(user_email);
CREATE INDEX IF NOT EXISTS idx_user_qr_services_service_type ON user_qr_services(service_type);
CREATE INDEX IF NOT EXISTS idx_user_qr_services_active ON user_qr_services(is_active);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_qr_services_updated_at 
    BEFORE UPDATE ON user_qr_services 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE user_qr_services IS 'Stores QR service metadata linked to users';
COMMENT ON COLUMN user_qr_services.service_type IS 'Type of service: wifi, upi, etc.';
COMMENT ON COLUMN user_qr_services.service_name IS 'User-friendly name for the service';
COMMENT ON COLUMN user_qr_services.metadata IS 'Service-specific data in JSON format';
COMMENT ON COLUMN user_qr_services.is_active IS 'Whether the service is currently active/enabled';
