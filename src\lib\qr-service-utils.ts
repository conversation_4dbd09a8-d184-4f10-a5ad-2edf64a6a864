// QR Service Utilities for validating and processing different service types

export interface WiFiQRData {
  ssid: string;
  password?: string;
  security?: 'WPA' | 'WEP' | 'nopass';
  hidden?: boolean;
}

export interface UPIQRData {
  pa: string; // UPI ID
  pn?: string; // Payee name
  am?: string; // Amount
  cu?: string; // Currency
  tn?: string; // Transaction note
}

export interface PaymentQRData {
  merchantId?: string; // Merchant identifier
  merchantName?: string; // Merchant name
  amount?: string; // Transaction amount
  currency?: string; // Currency code
  countryCode?: string; // Country code
  merchantCity?: string; // Merchant city
  paymentNetwork: string; // Payment network (Fonepay, eSewa, Khalti, etc.)
  additionalData?: string; // Additional transaction data
  rawData: string; // Raw QR code data for processing
}

export interface QRServiceMetadata {
  wifi?: WiFiQRData;
  upi?: UPIQRData;
  fonepay?: PaymentQRData;
  esewa?: PaymentQRData;
  khalti?: PaymentQRData;
  alipay?: PaymentQRData;
  wechatpay?: PaymentQRData;
  [key: string]: any;
}

/**
 * Parse WiFi QR code data
 * Format: WIFI:T:WPA;S:MyNetwork;P:MyPassword;H:false;;
 */
export function parseWiFiQR(qrData: string): WiFiQRData | null {
  try {
    // Check if it's a WiFi QR code
    if (!qrData.startsWith('WIFI:')) {
      return null;
    }

    const data: Partial<WiFiQRData> = {};
    
    // Remove WIFI: prefix and split by semicolons
    const params = qrData.substring(5).split(';');
    
    for (const param of params) {
      if (!param) continue;
      
      const [key, value] = param.split(':', 2);
      
      switch (key) {
        case 'S': // SSID
          data.ssid = value;
          break;
        case 'P': // Password
          data.password = value;
          break;
        case 'T': // Security type
          data.security = value as 'WPA' | 'WEP' | 'nopass';
          break;
        case 'H': // Hidden network
          data.hidden = value === 'true';
          break;
      }
    }

    // Validate required fields
    if (!data.ssid) {
      throw new Error('SSID is required for WiFi QR code');
    }

    return data as WiFiQRData;
  } catch (error) {
    console.error('Error parsing WiFi QR code:', error);
    return null;
  }
}

/**
 * Parse UPI QR code data
 * Format: upi://pay?pa=example@upi&pn=Name&am=100&cu=INR&tn=Payment
 */
export function parseUPIQR(qrData: string): UPIQRData | null {
  try {
    // Check if it's a UPI QR code
    if (!qrData.startsWith('upi://pay?')) {
      return null;
    }

    const url = new URL(qrData);
    const params = url.searchParams;

    const data: UPIQRData = {
      pa: params.get('pa') || '',
      pn: params.get('pn') || undefined,
      am: params.get('am') || undefined,
      cu: params.get('cu') || undefined,
      tn: params.get('tn') || undefined,
    };

    // Validate required fields
    if (!data.pa) {
      throw new Error('UPI ID (pa) is required for UPI QR code');
    }

    // Validate UPI ID format
    if (!isValidUPIId(data.pa)) {
      throw new Error('Invalid UPI ID format');
    }

    return data;
  } catch (error) {
    console.error('Error parsing UPI QR code:', error);
    return null;
  }
}

/**
 * Validate UPI ID format
 */
export function isValidUPIId(upiId: string): boolean {
  // UPI ID format: username@bank
  const upiRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/;
  return upiRegex.test(upiId);
}

/**
 * Parse payment QR code data for specific payment service
 * Used by various payment systems including Fonepay, eSewa, Khalti, Alipay, WeChat Pay
 * Format: Binary data with TLV (Tag-Length-Value) structure or service-specific formats
 */
export function parsePaymentQR(qrData: string, serviceType: string): PaymentQRData | null {
  try {
    const data: PaymentQRData = {
      rawData: qrData,
      paymentNetwork: getPaymentNetworkName(serviceType)
    };

    // Service-specific parsing patterns
    const servicePatterns = {
      fonepay: [/fonepay/i, /^[0-9]{10,}$/], // Fonepay patterns
      esewa: [/esewa/i, /^[0-9]{10,}$/], // eSewa patterns
      khalti: [/khalti/i, /^[0-9]{10,}$/], // Khalti patterns
      alipay: [/alipay/i, /^[0-9]{10,}$/], // Alipay patterns
      wechatpay: [/wechat/i, /^[0-9]{10,}$/] // WeChat Pay patterns
    };

    // Check if QR data matches the expected service type
    const patterns = servicePatterns[serviceType as keyof typeof servicePatterns];
    if (patterns) {
      const matches = patterns.some(pattern => pattern.test(qrData));
      if (!matches && qrData.length < 10) {
        return null; // Too short and doesn't match patterns
      }
    }

    // Try to parse EMVCo TLV structure if it's a numeric string
    if (/^[0-9]+$/.test(qrData) && qrData.length >= 10) {
      try {
        const tlvData = parseEMVCoTLV(qrData);
        Object.assign(data, tlvData);
      } catch (tlvError) {
        console.debug('TLV parsing failed, treating as raw payment data:', tlvError);
      }
    }

    // Extract basic information from URL-like formats
    if (qrData.includes('://') || qrData.includes('?')) {
      try {
        const urlData = parsePaymentURL(qrData);
        Object.assign(data, urlData);
      } catch (urlError) {
        console.debug('URL parsing failed:', urlError);
      }
    }

    return data;
  } catch (error) {
    console.error('Error parsing payment QR code:', error);
    return null;
  }
}

/**
 * Get payment network display name
 */
function getPaymentNetworkName(serviceType: string): string {
  const networkNames = {
    fonepay: 'Fonepay',
    esewa: 'eSewa',
    khalti: 'Khalti',
    alipay: 'Alipay',
    wechatpay: 'WeChat Pay'
  };
  return networkNames[serviceType as keyof typeof networkNames] || serviceType.toUpperCase();
}

/**
 * Parse payment URL format (for services that use URL-like QR codes)
 */
function parsePaymentURL(qrData: string): Partial<PaymentQRData> {
  try {
    const url = new URL(qrData);
    const params = url.searchParams;

    return {
      merchantId: params.get('merchant') || params.get('merchantId') || undefined,
      merchantName: params.get('merchantName') || params.get('name') || undefined,
      amount: params.get('amount') || params.get('amt') || undefined,
      currency: params.get('currency') || params.get('cur') || undefined,
      additionalData: params.get('note') || params.get('memo') || undefined,
    };
  } catch (error) {
    return {};
  }
}

/**
 * Parse EMVCo TLV (Tag-Length-Value) structure
 * This is a simplified parser for common EMVCo fields
 */
function parseEMVCoTLV(data: string): Partial<PaymentQRData> {
  const result: Partial<PaymentQRData> = {};

  try {
    let index = 0;
    while (index < data.length - 4) {
      const tag = data.substring(index, index + 2);
      const length = parseInt(data.substring(index + 2, index + 4), 10);

      if (isNaN(length) || index + 4 + length > data.length) {
        break;
      }

      const value = data.substring(index + 4, index + 4 + length);

      // Map common EMVCo tags to fields
      switch (tag) {
        case '00': // Payload Format Indicator
          break;
        case '01': // Point of Initiation Method
          break;
        case '52': // Merchant Category Code
          break;
        case '53': // Transaction Currency
          result.currency = value;
          break;
        case '54': // Transaction Amount
          result.amount = value;
          break;
        case '58': // Country Code
          result.countryCode = value;
          break;
        case '59': // Merchant Name
          result.merchantName = value;
          break;
        case '60': // Merchant City
          result.merchantCity = value;
          break;
        case '62': // Additional Data Field Template
          result.additionalData = value;
          break;
      }

      index += 4 + length;
    }
  } catch (error) {
    console.debug('TLV parsing error:', error);
  }

  return result;
}

/**
 * Parse QR code data based on service type
 */
export function parseQRData(qrData: string, serviceType: string): QRServiceMetadata | null {
  switch (serviceType) {
    case 'wifi':
      const wifiData = parseWiFiQR(qrData);
      return wifiData ? { wifi: wifiData } : null;

    case 'upi':
      const upiData = parseUPIQR(qrData);
      return upiData ? { upi: upiData } : null;

    case 'fonepay':
      const fonepayData = parsePaymentQR(qrData, 'fonepay');
      return fonepayData ? { fonepay: fonepayData } : null;

    case 'esewa':
      const esewaData = parsePaymentQR(qrData, 'esewa');
      return esewaData ? { esewa: esewaData } : null;

    case 'khalti':
      const khaltiData = parsePaymentQR(qrData, 'khalti');
      return khaltiData ? { khalti: khaltiData } : null;

    case 'alipay':
      const alipayData = parsePaymentQR(qrData, 'alipay');
      return alipayData ? { alipay: alipayData } : null;

    case 'wechatpay':
      const wechatpayData = parsePaymentQR(qrData, 'wechatpay');
      return wechatpayData ? { wechatpay: wechatpayData } : null;

    default:
      console.error('Unsupported service type:', serviceType);
      return null;
  }
}

/**
 * Validate service metadata
 */
export function validateServiceMetadata(serviceType: string, metadata: any): { isValid: boolean; error?: string } {
  try {
    switch (serviceType) {
      case 'wifi':
        if (!metadata.wifi || !metadata.wifi.ssid) {
          return { isValid: false, error: 'WiFi SSID is required' };
        }
        if (metadata.wifi.ssid.length > 32) {
          return { isValid: false, error: 'WiFi SSID cannot exceed 32 characters' };
        }
        break;
      
      case 'upi':
        if (!metadata.upi || !metadata.upi.pa) {
          return { isValid: false, error: 'UPI ID is required' };
        }
        if (!isValidUPIId(metadata.upi.pa)) {
          return { isValid: false, error: 'Invalid UPI ID format' };
        }
        break;

      case 'fonepay':
      case 'esewa':
      case 'khalti':
      case 'alipay':
      case 'wechatpay':
        const paymentData = metadata[serviceType];
        if (!paymentData || !paymentData.rawData) {
          return { isValid: false, error: `${getPaymentNetworkName(serviceType)} QR data is required` };
        }
        if (paymentData.rawData.length < 10) {
          return { isValid: false, error: `${getPaymentNetworkName(serviceType)} QR data appears to be too short` };
        }
        break;

      default:
        return { isValid: false, error: 'Unsupported service type' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Validation error occurred' };
  }
}

/**
 * Generate user-friendly service name from metadata
 */
export function generateServiceName(serviceType: string, metadata: any): string {
  switch (serviceType) {
    case 'wifi':
      return metadata.wifi?.ssid || 'WiFi Network';
    
    case 'upi':
      return metadata.upi?.pn || metadata.upi?.pa || 'UPI Payment';

    case 'fonepay':
      return metadata.fonepay?.merchantName || 'Fonepay Payment';

    case 'esewa':
      return metadata.esewa?.merchantName || 'eSewa Payment';

    case 'khalti':
      return metadata.khalti?.merchantName || 'Khalti Payment';

    case 'alipay':
      return metadata.alipay?.merchantName || 'Alipay Payment';

    case 'wechatpay':
      return metadata.wechatpay?.merchantName || 'WeChat Pay Payment';

    default:
      return `${serviceType.toUpperCase()} Service`;
  }
}

/**
 * Get service display information
 */
export function getServiceDisplayInfo(serviceType: string) {
  switch (serviceType) {
    case 'wifi':
      return {
        name: 'WiFi',
        icon: '/qr/wifi.png',
        description: 'Connect to WiFi network',
        color: 'blue'
      };

    case 'upi':
      return {
        name: 'UPI Payment',
        icon: '/qr/upi.png',
        description: 'Make UPI payment',
        color: 'green'
      };

    case 'fonepay':
      return {
        name: 'Fonepay',
        icon: '/qr/fonepay.png',
        description: 'Fonepay digital payment',
        color: 'orange'
      };

    case 'esewa':
      return {
        name: 'eSewa',
        icon: '/qr/esewa.png',
        description: 'eSewa digital wallet payment',
        color: 'green'
      };

    case 'khalti':
      return {
        name: 'Khalti',
        icon: '/qr/khalti.png',
        description: 'Khalti digital wallet payment',
        color: 'purple'
      };

    case 'alipay':
      return {
        name: 'Alipay',
        icon: '/qr/alipay.png',
        description: 'Alipay mobile payment',
        color: 'blue'
      };

    case 'wechatpay':
      return {
        name: 'WeChat Pay',
        icon: '/qr/wechatpay.png',
        description: 'WeChat Pay mobile payment',
        color: 'green'
      };

    default:
      return {
        name: serviceType.toUpperCase(),
        icon: '/qr/default.png',
        description: `${serviceType} service`,
        color: 'gray'
      };
  }
}
