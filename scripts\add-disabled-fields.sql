-- Migration script to add disabled fields to profiles and contact tables
-- This script adds the disabled boolean column for admin toggle functionality

-- Add disabled column to profiles table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'disabled'
    ) THEN
        ALTER TABLE profiles ADD COLUMN disabled BOOLEAN DEFAULT FALSE NOT NULL;
        
        -- Add index for the new column for better performance
        CREATE INDEX IF NOT EXISTS idx_profiles_disabled ON profiles (disabled);
        
        -- Add comment for documentation
        COMMENT ON COLUMN profiles.disabled IS 'Whether the profile is disabled by admin. Disabled profiles cannot login.';
        
        RAISE NOTICE 'Added disabled column to profiles table';
    ELSE
        RAISE NOTICE 'Disabled column already exists in profiles table';
    END IF;
END $$;

-- Add disabled column to contact table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'contact' 
        AND column_name = 'disabled'
    ) THEN
        ALTER TABLE contact ADD COLUMN disabled BOOLEAN DEFAULT FALSE NOT NULL;
        
        -- Add index for the new column for better performance
        CREATE INDEX IF NOT EXISTS idx_contact_disabled ON contact (disabled);
        
        -- Add comment for documentation
        COMMENT ON COLUMN contact.disabled IS 'Whether the contact is disabled by admin. Disabled contacts show temporary disabled message.';
        
        RAISE NOTICE 'Added disabled column to contact table';
    ELSE
        RAISE NOTICE 'Disabled column already exists in contact table';
    END IF;
END $$;

-- Verification queries
SELECT 'Disabled fields migration completed successfully' as status;
SELECT 
    table_name, 
    column_name, 
    data_type, 
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('profiles', 'contact') 
AND column_name = 'disabled'
ORDER BY table_name;
